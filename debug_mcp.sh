#!/bin/bash

echo "=== MCP 服务器调试脚本 ==="

echo "1. 测试 MCP 服务器是否能正常启动..."
timeout 3s ./target/release/cunzhi-mcp &
MCP_PID=$!
sleep 1

if kill -0 $MCP_PID 2>/dev/null; then
    echo "✅ MCP 服务器启动成功 (PID: $MCP_PID)"
    kill $MCP_PID
else
    echo "❌ MCP 服务器启动失败"
fi

echo ""
echo "2. 测试主应用启动（无显示环境）..."
timeout 5s ./target/release/cunzhi-main --help 2>&1 | head -10

echo ""
echo "3. 检查依赖库..."
ldd ./target/release/cunzhi-main | grep -E "(not found|missing)"

echo ""
echo "4. 检查文件权限..."
ls -la ./target/release/cunzhi-*

echo ""
echo "5. 测试 MCP 协议通信..."
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0"}}}' | timeout 3s ./target/release/cunzhi-mcp 2>&1 | head -5

echo ""
echo "=== 调试完成 ==="
