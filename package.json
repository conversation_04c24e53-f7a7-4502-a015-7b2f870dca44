{"name": "cunzhi", "type": "module", "version": "0.2.12", "packageManager": "pnpm@10.12.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "tauri": "cargo tauri", "tauri:dev": "cargo tauri dev", "tauri:build": "cargo tauri build"}, "dependencies": {"@tauri-apps/api": "^1.6.0", "@types/markdown-it": "^14.1.2", "@vueuse/core": "^13.3.0", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "uuid": "^11.1.0", "vue": "^3.5.16"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@iconify-json/carbon": "^1.2.9", "@unocss/preset-attributify": "^66.2.1", "@unocss/preset-icons": "^66.2.1", "@unocss/preset-typography": "^66.2.1", "@unocss/preset-web-fonts": "^66.2.1", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "naive-ui": "^2.41.1", "unocss": "^66.2.1", "vite": "^6.3.5"}}