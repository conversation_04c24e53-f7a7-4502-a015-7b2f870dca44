#!/bin/bash

echo "=== 寸止连接测试脚本 ==="
echo "测试 cunzhi-mcp 和 cunzhi-main 之间的连接"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理进程..."
    if [ ! -z "$MCP_PID" ]; then
        kill $MCP_PID 2>/dev/null
        echo "   - 已终止 MCP 服务器 (PID: $MCP_PID)"
    fi
    if [ ! -z "$MAIN_PID" ]; then
        kill $MAIN_PID 2>/dev/null
        echo "   - 已终止主应用 (PID: $MAIN_PID)"
    fi
    rm -f test_mcp_request.json mcp_output.log main_output.log
    echo "   - 已清理临时文件"
}

# 设置信号处理
trap cleanup EXIT INT TERM

echo "1️⃣  准备测试环境..."

# 创建测试 MCP 请求文件
cat > test_mcp_request.json << 'EOF'
{
  "id": "connection-test-123",
  "message": "连接测试：这是一个用于测试 MCP 连接的消息。",
  "predefined_options": ["测试选项1", "测试选项2", "取消测试"],
  "is_markdown": false
}
EOF

echo "✅ 创建测试 MCP 请求文件"

echo ""
echo "2️⃣  启动 MCP 服务器..."

# 启动 MCP 服务器并记录输出
RUST_LOG=debug ./target/release/cunzhi-mcp > mcp_output.log 2>&1 &
MCP_PID=$!

echo "✅ MCP 服务器已启动 (PID: $MCP_PID)"
echo "📝 MCP 输出日志: mcp_output.log"

# 等待 MCP 服务器启动
sleep 2

# 检查 MCP 服务器是否还在运行
if ! kill -0 $MCP_PID 2>/dev/null; then
    echo "❌ MCP 服务器启动失败"
    echo "📋 MCP 服务器输出:"
    cat mcp_output.log
    exit 1
fi

echo "✅ MCP 服务器运行正常"

echo ""
echo "3️⃣  测试 MCP 协议通信..."

# 测试 MCP 协议
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0"}}}' | timeout 3s ./target/release/cunzhi-mcp > mcp_test_output.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ MCP 协议通信正常"
    echo "📋 MCP 响应:"
    head -3 mcp_test_output.log
else
    echo "❌ MCP 协议通信失败"
    echo "📋 MCP 错误输出:"
    cat mcp_test_output.log
fi

echo ""
echo "4️⃣  启动主应用（GUI 模式）..."

# 启动主应用并记录详细输出
RUST_LOG=debug ./target/release/cunzhi-main > main_output.log 2>&1 &
MAIN_PID=$!

echo "✅ 主应用已启动 (PID: $MAIN_PID)"
echo "📝 主应用输出日志: main_output.log"

# 等待主应用启动或失败
sleep 5

# 检查主应用状态
if kill -0 $MAIN_PID 2>/dev/null; then
    echo "✅ 主应用仍在运行"
else
    echo "❌ 主应用已退出"
    echo "📋 主应用输出:"
    cat main_output.log
fi

echo ""
echo "5️⃣  测试 MCP 请求模式..."

# 测试 MCP 请求模式
echo "🔗 测试 MCP 请求处理..."
timeout 10s ./target/release/cunzhi-main --mcp-request test_mcp_request.json > mcp_request_output.log 2>&1

echo "📋 MCP 请求模式输出:"
cat mcp_request_output.log

echo ""
echo "6️⃣  分析连接状态..."

echo "🔍 进程状态:"
if kill -0 $MCP_PID 2>/dev/null; then
    echo "   - MCP 服务器: 运行中 ✅"
else
    echo "   - MCP 服务器: 已停止 ❌"
fi

if kill -0 $MAIN_PID 2>/dev/null; then
    echo "   - 主应用: 运行中 ✅"
else
    echo "   - 主应用: 已停止 ❌"
fi

echo ""
echo "🔍 网络连接:"
netstat -tlnp 2>/dev/null | grep -E "(cunzhi|$(echo $MCP_PID)|$(echo $MAIN_PID))" || echo "   - 未发现相关网络连接"

echo ""
echo "📊 测试总结:"
echo "   - MCP 服务器 PID: $MCP_PID"
echo "   - 主应用 PID: $MAIN_PID"
echo "   - 日志文件: mcp_output.log, main_output.log"
echo ""
echo "💡 查看详细日志:"
echo "   tail -f mcp_output.log"
echo "   tail -f main_output.log"

echo ""
echo "⏳ 保持运行 10 秒以观察行为..."
sleep 10

echo ""
echo "=== 测试完成 ==="
