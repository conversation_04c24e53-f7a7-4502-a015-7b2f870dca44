{"$schema": "https://schema.tauri.app/config/1.0.0", "package": {"productName": "cunzhi", "version": "0.2.12"}, "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "distDir": "./dist", "devPath": "http://localhost:5176"}, "tauri": {"allowlist": {"all": false, "protocol": {"asset": true, "assetScope": ["**"]}, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "minimize": true, "setAlwaysOnTop": true, "setFocus": true}}, "windows": [{"title": "cunzhi", "width": 600, "height": 800, "minWidth": 600, "minHeight": 400, "maxWidth": 600, "maxHeight": 1200, "center": true, "visible": true, "alwaysOnTop": true, "decorations": true, "transparent": false, "skipTaskbar": false, "resizable": true, "fullscreen": false}], "security": {"csp": null}, "bundle": {"active": true, "targets": "all", "identifier": "com.imhuso.cunzhi", "icon": ["icons/icon-32.png", "icons/icon-128.png", "icons/icon-256.png", "icons/icon-512.png", "icons/icon.ico", "icons/icon.icns"]}, "updater": {"active": true, "endpoints": ["https://api.github.com/repos/imhuso/cunzhi/releases/latest"], "dialog": false, "pubkey": ""}}}