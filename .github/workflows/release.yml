name: Build CLI Tools

on:
  push:
    tags: ['v*']
  workflow_dispatch:

jobs:
  build-cli:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: macos-latest
            args: --target aarch64-apple-darwin
            name: macos-aarch64
          - platform: macos-latest
            args: --target x86_64-apple-darwin
            name: macos-x86_64
          - platform: ubuntu-22.04
            args: ''
            name: linux-x86_64
          - platform: windows-latest
            args: ''
            name: windows-x86_64

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - name: Install system dependencies (Linux)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libwebkit2gtk-4.1-dev \
            libappindicator3-dev \
            librsvg2-dev \
            patchelf \
            pkg-config \
            libglib2.0-dev \
            libgtk-3-dev \
            libgdk-pixbuf2.0-dev \
            libpango1.0-dev \
            libatk1.0-dev \
            libcairo-gobject2 \
            libjavascriptcoregtk-4.1-dev \
            libasound2-dev \
            libpulse-dev \
            libjack-dev

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2

      - name: Install frontend dependencies
        run: pnpm install

      - name: Install Tauri CLI
        run: cargo install tauri-cli --version "^2.0" --locked

      - name: Build with Tauri (no bundle)
        shell: bash
        run: |
          if [[ "${{ matrix.platform }}" == "macos-latest" ]]; then
            if [[ "${{ matrix.args }}" == *"aarch64"* ]]; then
              cargo tauri build --target aarch64-apple-darwin --no-bundle
            else
              cargo tauri build --target x86_64-apple-darwin --no-bundle
            fi
          elif [[ "${{ matrix.platform }}" == "windows-latest" ]]; then
            cargo tauri build --no-bundle
          else
            cargo tauri build --no-bundle
          fi

      - name: Create CLI package
        shell: bash
        run: |
          mkdir -p cli-package

          # 获取完整的 tag 名称
          TAG_NAME="${{ github.ref_name }}"

          # 确定目标目录
          if [[ "${{ matrix.platform }}" == "macos-latest" ]]; then
            if [[ "${{ matrix.args }}" == *"aarch64"* ]]; then
              TARGET_DIR="target/aarch64-apple-darwin/release"
            else
              TARGET_DIR="target/x86_64-apple-darwin/release"
            fi
          elif [[ "${{ matrix.platform }}" == "windows-latest" ]]; then
            TARGET_DIR="target/release"
          else
            TARGET_DIR="target/release"
          fi

          # 复制二进制文件
          if [[ "${{ matrix.platform }}" == "windows-latest" ]]; then
            cp "$TARGET_DIR/等一下.exe" cli-package/
            cp "$TARGET_DIR/寸止.exe" cli-package/
            cd cli-package
            7z a ../cunzhi-cli-${TAG_NAME}-${{ matrix.name }}.zip *
          else
            cp "$TARGET_DIR/等一下" cli-package/
            cp "$TARGET_DIR/寸止" cli-package/
            cd cli-package
            tar -czf ../cunzhi-cli-${TAG_NAME}-${{ matrix.name }}.tar.gz *
          fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cunzhi-cli-${{ matrix.name }}
          path: |
            cunzhi-cli-*.tar.gz
            cunzhi-cli-*.zip
          if-no-files-found: ignore

  release:
    name: Create Release
    needs: build-cli
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      - name: Verify version consistency
        run: |
          # 获取 git tag 版本号（去掉 v 前缀）
          TAG_NAME="${{ github.ref_name }}"
          TAG_VERSION_NUMBER=${TAG_NAME#v}

          # 从项目文件读取版本号
          if [ -f "version.json" ]; then
            PROJECT_VERSION=$(grep -o '"version"[[:space:]]*:[[:space:]]*"[^"]*"' version.json | cut -d'"' -f4)
          elif [ -f "Cargo.toml" ]; then
            PROJECT_VERSION=$(grep '^version = ' Cargo.toml | head -1 | cut -d'"' -f2)
          else
            echo "Warning: No version file found, skipping version check"
            exit 0
          fi

          echo "Git tag version: ${TAG_VERSION_NUMBER}"
          echo "Project file version: ${PROJECT_VERSION}"

          # 检查版本是否一致
          if [ "${TAG_VERSION_NUMBER}" != "${PROJECT_VERSION}" ]; then
            echo "❌ Version mismatch detected!"
            echo "Git tag version: ${TAG_VERSION_NUMBER}"
            echo "Project file version: ${PROJECT_VERSION}"
            echo ""
            echo "Please ensure the git tag matches the version in project files."
            echo "You can either:"
            echo "1. Update project files to match tag: ${TAG_VERSION_NUMBER}"
            echo "2. Create a new tag that matches project version: v${PROJECT_VERSION}"
            exit 1
          fi

          echo "✅ Version consistency check passed: ${TAG_VERSION_NUMBER}"

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Install git-cliff
        uses: taiki-e/install-action@git-cliff

      - name: Generate changelog
        id: changelog
        run: |
          # 获取上一个版本标签
          PREVIOUS_TAG=$(git tag --sort=-version:refname | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | head -2 | tail -1)

          echo "Current tag: ${{ github.ref_name }}"
          echo "Previous tag: $PREVIOUS_TAG"

          if [ -z "$PREVIOUS_TAG" ]; then
            # 如果没有上一个版本，生成所有提交的 changelog
            git-cliff --tag ${{ github.ref_name }} --output changelog.md
          else
            # 只生成从上一个版本到当前版本的 changelog
            git-cliff $PREVIOUS_TAG..${{ github.ref_name }} --output changelog.md
          fi

          # 简化的发布信息
          {
            echo "## Release Notes"
            echo ""
            cat changelog.md
          } > temp_changelog.md

          mv temp_changelog.md changelog.md

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            artifacts/*/cunzhi-cli-*.tar.gz
            artifacts/*/cunzhi-cli-*.zip
          draft: false
          prerelease: false
          generate_release_notes: false
          name: Cunzhi ${{ github.ref_name }}
          body_path: changelog.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Homebrew Formula Update
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          script: |
            console.log('Triggering Homebrew formula update...');

            try {
              const response = await github.rest.actions.createWorkflowDispatch({
                owner: context.repo.owner,
                repo: context.repo.repo,
                workflow_id: 'update-homebrew.yml',
                ref: 'main',
                inputs: {
                  tag_name: '${{ github.ref_name }}'
                }
              });

              console.log('✅ Homebrew update workflow triggered successfully');
            } catch (error) {
              console.error('❌ Failed to trigger Homebrew update workflow:', error);
              // 不让这个错误阻止主要的发布流程
              console.log('Continuing with release process...');
            }
