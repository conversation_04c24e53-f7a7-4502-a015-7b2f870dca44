use crate::config::load_standalone_telegram_config;
use crate::telegram::handle_telegram_only_mcp_request;
use crate::log_important;
use crate::app::builder::run_tauri_app;
use anyhow::Result;

/// 处理命令行参数
pub fn handle_cli_args() -> Result<()> {
    let args: Vec<String> = std::env::args().collect();
    eprintln!("🔍 解析命令行参数，参数数量: {}", args.len());

    match args.len() {
        // 无参数：正常启动GUI
        1 => {
            eprintln!("🖥️  启动 GUI 模式...");

            // 检查图形环境
            let has_display = std::env::var("DISPLAY").is_ok() || std::env::var("WAYLAND_DISPLAY").is_ok();

            if !has_display {
                eprintln!("⚠️  警告: 未检测到图形显示环境");
                eprintln!("");
                eprintln!("🔍 诊断信息:");
                eprintln!("   - DISPLAY 环境变量: {:?}", std::env::var("DISPLAY"));
                eprintln!("   - WAYLAND_DISPLAY 环境变量: {:?}", std::env::var("WAYLAND_DISPLAY"));
                eprintln!("");
                eprintln!("💡 正常情况下需要图形环境，但我们将尝试启动以获取详细错误信息...");
                eprintln!("📝 这将帮助我们诊断具体的连接问题");
                eprintln!("");
            }

            eprintln!("✅ 检测到图形显示环境");
            eprintln!("🔧 准备初始化 Tauri 应用...");

            match std::panic::catch_unwind(|| {
                run_tauri_app();
            }) {
                Ok(_) => eprintln!("✅ Tauri 应用正常退出"),
                Err(e) => {
                    eprintln!("❌ Tauri 应用崩溃: {:?}", e);
                    return Err(anyhow::anyhow!("Tauri 应用启动失败"));
                }
            }
        }
        // 单参数：帮助或版本
        2 => {
            eprintln!("📖 处理单参数命令: {}", args[1]);
            match args[1].as_str() {
                "--help" | "-h" => {
                    eprintln!("📋 显示帮助信息");
                    print_help();
                }
                "--version" | "-v" => {
                    eprintln!("🏷️  显示版本信息");
                    print_version();
                }
                _ => {
                    eprintln!("❌ 未知参数: {}", args[1]);
                    print_help();
                    std::process::exit(1);
                }
            }
        }
        // 多参数：MCP请求模式
        _ => {
            eprintln!("📨 处理多参数命令...");
            if args[1] == "--mcp-request" && args.len() >= 3 {
                eprintln!("🔗 MCP 请求模式，文件: {}", args[2]);
                handle_mcp_request(&args[2])?;
            } else {
                eprintln!("❌ 无效的命令行参数组合");
                print_help();
                std::process::exit(1);
            }
        }
    }

    Ok(())
}

/// 处理MCP请求
fn handle_mcp_request(request_file: &str) -> Result<()> {
    // 检查Telegram配置，决定是否启用纯Telegram模式
    match load_standalone_telegram_config() {
        Ok(telegram_config) => {
            if telegram_config.enabled && telegram_config.hide_frontend_popup {
                // 纯Telegram模式：不启动GUI，直接处理
                if let Err(e) = tokio::runtime::Runtime::new()
                    .unwrap()
                    .block_on(handle_telegram_only_mcp_request(request_file))
                {
                    log_important!(error, "处理Telegram请求失败: {}", e);
                    std::process::exit(1);
                }
            } else {
                // 正常模式：启动GUI处理弹窗
                run_tauri_app();
            }
        }
        Err(e) => {
            log_important!(warn, "加载Telegram配置失败: {}，使用默认GUI模式", e);
            // 配置加载失败时，使用默认行为（启动GUI）
            run_tauri_app();
        }
    }
    Ok(())
}

/// 显示帮助信息
fn print_help() {
    println!("寸止 - 智能代码审查工具");
    println!("告别AI提前终止烦恼，助力AI更加持久");
    println!();
    println!("用法:");
    println!("  cunzhi-main                      启动图形界面（需要显示环境）");
    println!("  cunzhi-main --mcp-request <文件>  处理 MCP 请求（无头模式）");
    println!("  cunzhi-main --help               显示此帮助信息");
    println!("  cunzhi-main --version            显示版本信息");
    println!();
    println!("环境要求:");
    println!("  图形界面模式:");
    println!("    - 需要 X11 或 Wayland 显示服务器");
    println!("    - 设置 DISPLAY 或 WAYLAND_DISPLAY 环境变量");
    println!("    - 例如: export DISPLAY=:0");
    println!();
    println!("  MCP 模式:");
    println!("    - 无需图形环境，可在服务器上运行");
    println!("    - 支持 Telegram 集成");
    println!();
    println!("故障排除:");
    println!("  如果遇到 'Could not connect' 错误:");
    println!("    1. 检查是否有图形显示环境");
    println!("    2. 尝试使用 SSH X11 转发: ssh -X user@host");
    println!("    3. 使用虚拟显示: Xvfb :1 -screen 0 1024x768x24 &");
    println!("       然后设置: export DISPLAY=:1");
    println!("    4. 使用 MCP 模式代替图形模式");
    println!();
    println!("更多信息:");
    println!("  项目主页: https://github.com/imhuso/cunzhi");
    println!("  文档: https://github.com/imhuso/cunzhi/blob/main/README.md");
}

/// 显示版本信息
fn print_version() {
    println!("寸止 v{}", env!("CARGO_PKG_VERSION"));
}
