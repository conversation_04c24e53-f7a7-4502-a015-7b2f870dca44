use crate::config::AppState;
use crate::ui::AudioController;
use crate::app::{setup::setup_application, commands::*};
use crate::log_important;
use std::sync::atomic::AtomicBool;
use std::sync::Arc;
use std::error::Error;
use tauri::{<PERSON><PERSON><PERSON>, Manager};

/// 构建Tauri应用
pub fn build_tauri_app() -> Builder<tauri::Wry> {
    eprintln!("🔨 开始构建 Tauri 应用...");

    eprintln!("📦 创建默认 Builder...");
    let builder = tauri::Builder::default();

    eprintln!("🗃️  管理应用状态...");
    let builder = builder.manage(AppState::default());

    eprintln!("🔊 管理音频控制器...");
    let builder = builder.manage(AudioController {
        should_stop: Arc::new(AtomicBool::new(false)),
    });

    eprintln!("⚙️  注册 Tauri 命令...");
    builder.invoke_handler(tauri::generate_handler![
            // 基础应用命令
            get_app_info,
            get_always_on_top,
            set_always_on_top,
            sync_window_state,

            // 音频命令
            get_audio_notification_enabled,
            set_audio_notification_enabled,
            get_audio_url,
            set_audio_url,
            play_notification_sound,
            test_audio_sound,
            stop_audio_sound,
            get_available_audio_assets,
            refresh_audio_assets,

            // 主题和窗口命令
            get_theme,
            set_theme,
            get_window_config,
            set_window_config,
            get_reply_config,
            set_reply_config,
            get_window_settings,
            set_window_settings,
            get_window_settings_for_mode,
            get_window_constraints_cmd,
            get_current_window_size,
            apply_window_constraints,
            update_window_size,

            // MCP 命令
            get_mcp_tools_config,
            set_mcp_tool_enabled,
            get_mcp_tools_status,
            reset_mcp_tools_config,
            send_mcp_response,
            get_cli_args,
            read_mcp_request,
            select_image_files,
            build_mcp_send_response,
            build_mcp_continue_response,

            // Telegram 命令
            get_telegram_config,
            set_telegram_config,
            test_telegram_connection_cmd,
            auto_get_chat_id,
            start_telegram_sync,

            // 系统命令
            open_external_url,
            exit_app,
            handle_app_exit_request,
            force_exit_app,
            reset_exit_attempts_cmd,

            // 更新命令
            check_for_updates,
            download_and_install_update,
            get_current_version,
            restart_app
        ])
        .setup(|app| {
            eprintln!("🔧 Tauri setup 回调开始...");

            // 获取应用信息
            eprintln!("📱 应用信息:");
            eprintln!("   - 应用名称: {:?}", app.package_info().name);
            eprintln!("   - 应用版本: {:?}", app.package_info().version);

            let app_handle = app.handle();
            eprintln!("✅ 获取 app handle 成功");

            // 尝试获取窗口信息
            eprintln!("🪟 检查窗口状态...");
            match app.get_window("main") {
                Some(window) => {
                    eprintln!("✅ 找到主窗口");
                    match window.is_visible() {
                        Ok(visible) => eprintln!("   - 窗口可见性: {}", visible),
                        Err(e) => eprintln!("   - 无法获取窗口可见性: {}", e),
                    }
                }
                None => eprintln!("⚠️  未找到主窗口（可能还未创建）"),
            }

            // 应用初始化
            eprintln!("⚙️  开始异步应用初始化...");
            tauri::async_runtime::block_on(async {
                match setup_application(&app_handle).await {
                    Ok(_) => {
                        eprintln!("✅ 应用初始化完全成功");
                    }
                    Err(e) => {
                        eprintln!("❌ 应用初始化失败: {}", e);
                        log_important!(error, "应用初始化失败: {}", e);

                        // 不要因为初始化失败就退出，继续尝试启动
                        eprintln!("⚠️  尽管初始化失败，仍尝试继续启动应用");
                    }
                }
            });

            eprintln!("🎉 Tauri setup 回调完成");
            Ok(())
        })
}

/// 运行Tauri应用
pub fn run_tauri_app() {
    eprintln!("🏗️  构建 Tauri 应用...");

    let app_builder = build_tauri_app();
    eprintln!("✅ Tauri 应用构建完成");

    eprintln!("🚀 启动 Tauri 应用...");
    eprintln!("🔍 Tauri 上下文信息:");
    eprintln!("   - 包名: {}", env!("CARGO_PKG_NAME"));
    eprintln!("   - 版本: {}", env!("CARGO_PKG_VERSION"));

    // 尝试获取更多环境信息
    eprintln!("🌍 环境信息:");
    eprintln!("   - 当前用户: {:?}", std::env::var("USER").or_else(|_| std::env::var("USERNAME")));
    eprintln!("   - HOME: {:?}", std::env::var("HOME"));
    eprintln!("   - XDG_RUNTIME_DIR: {:?}", std::env::var("XDG_RUNTIME_DIR"));
    eprintln!("   - WAYLAND_DISPLAY: {:?}", std::env::var("WAYLAND_DISPLAY"));
    eprintln!("   - DISPLAY: {:?}", std::env::var("DISPLAY"));

    match app_builder.run(tauri::generate_context!()) {
        Ok(_) => {
            eprintln!("✅ Tauri 应用正常退出");
        }
        Err(e) => {
            eprintln!("❌ Tauri 应用运行失败: {}", e);
            eprintln!("🔍 错误类型: {:?}", std::any::type_name_of_val(&e));

            // 分析错误源
            let mut source = e.source();
            let mut level = 1;
            while let Some(err) = source {
                eprintln!("🔗 错误源 {}: {}", level, err);
                source = err.source();
                level += 1;
                if level > 5 { break; } // 防止无限循环
            }

            // 提供更详细的错误信息
            let error_str = e.to_string();
            eprintln!("📝 完整错误信息: {}", error_str);

            if error_str.contains("Could not connect") {
                eprintln!("🔍 连接错误分析:");
                eprintln!("   - 这通常是 Tauri webview 无法连接到显示服务器");
                eprintln!("   - 检查 DISPLAY 或 WAYLAND_DISPLAY 环境变量");
                eprintln!("   - 确保有可用的图形显示环境");
                eprintln!("   - 尝试: export DISPLAY=:0");
            } else if error_str.contains("Unable to init server") {
                eprintln!("🔍 服务器初始化错误分析:");
                eprintln!("   - Tauri 内部服务器初始化失败");
                eprintln!("   - 可能是 IPC 或 webview 服务器启动失败");
                eprintln!("   - 检查系统资源和权限");
            } else if error_str.contains("display") {
                eprintln!("🔍 显示相关错误分析:");
                eprintln!("   - GTK/显示系统错误");
                eprintln!("   - 需要图形显示环境");
            }

            eprintln!("💡 建议的解决方案:");
            eprintln!("   1. 在有图形界面的环境中运行");
            eprintln!("   2. 使用 SSH X11 转发");
            eprintln!("   3. 使用虚拟显示服务器");
            eprintln!("   4. 使用 MCP 模式（如果支持）");

            eprintln!("🚨 Tauri 应用启动失败，但不会 panic，而是正常退出");
            std::process::exit(1);
        }
    }
}
