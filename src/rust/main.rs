use cunzhi::app::{handle_cli_args, run_tauri_app};
use cunzhi::utils::auto_init_logger;
use anyhow::Result;
use std::env;
use std::error::Error;

pub fn run() {
    run_tauri_app();
}

fn main() -> Result<()> {
    // 设置 panic hook 来捕获详细的错误信息
    std::panic::set_hook(Box::new(|panic_info| {
        eprintln!("💥 应用发生 PANIC!");
        eprintln!("📍 位置: {}", panic_info.location().map_or("未知".to_string(), |l| format!("{}:{}:{}", l.file(), l.line(), l.column())));

        if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
            eprintln!("📝 错误信息: {}", s);
        } else if let Some(s) = panic_info.payload().downcast_ref::<String>() {
            eprintln!("📝 错误信息: {}", s);
        } else {
            eprintln!("📝 错误信息: 未知类型的 panic");
        }

        eprintln!("🔍 调用栈:");
        eprintln!("{}", std::backtrace::Backtrace::force_capture());

        eprintln!("💡 这可能是由于:");
        eprintln!("   - 图形显示环境不可用");
        eprintln!("   - Tauri webview 初始化失败");
        eprintln!("   - 系统资源不足");
        eprintln!("   - 权限问题");
    }));

    // 输出启动信息
    eprintln!("🚀 寸止应用启动中...");
    eprintln!("📍 当前工作目录: {:?}", env::current_dir().unwrap_or_default());
    eprintln!("🔧 命令行参数: {:?}", env::args().collect::<Vec<_>>());

    // 检查显示环境
    match env::var("DISPLAY") {
        Ok(display) => eprintln!("🖥️  DISPLAY 环境变量: {}", display),
        Err(_) => eprintln!("⚠️  未设置 DISPLAY 环境变量（无头环境）"),
    }

    // 检查 Wayland 环境
    match env::var("WAYLAND_DISPLAY") {
        Ok(wayland) => eprintln!("🖥️  WAYLAND_DISPLAY 环境变量: {}", wayland),
        Err(_) => eprintln!("ℹ️  未设置 WAYLAND_DISPLAY 环境变量"),
    }

    // 初始化日志系统
    eprintln!("📝 初始化日志系统...");
    if let Err(e) = auto_init_logger() {
        eprintln!("❌ 初始化日志系统失败: {}", e);
    } else {
        eprintln!("✅ 日志系统初始化成功");
    }

    // 处理命令行参数
    eprintln!("⚙️  处理命令行参数...");
    match handle_cli_args() {
        Ok(_) => {
            eprintln!("✅ 应用正常退出");
            Ok(())
        }
        Err(e) => {
            eprintln!("❌ 应用异常退出: {}", e);

            // 输出更多调试信息
            eprintln!("🔍 错误详情:");
            eprintln!("   - 错误类型: {}", std::any::type_name_of_val(&e));
            eprintln!("   - 错误消息: {}", e);

            let mut source = e.source();
            let mut level = 1;
            while let Some(err) = source {
                eprintln!("   - 错误源 {}: {}", level, err);
                source = err.source();
                level += 1;
                if level > 3 { break; }
            }

            Err(e)
        }
    }
}


