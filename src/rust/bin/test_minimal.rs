// 最小化测试程序 - 用于隔离 Tauri 启动问题
use std::env;

fn main() {
    eprintln!("🧪 最小化 Tauri 测试程序启动");
    
    // 检查环境
    eprintln!("🔍 环境检查:");
    eprintln!("   DISPLAY: {:?}", env::var("DISPLAY"));
    eprintln!("   WAYLAND_DISPLAY: {:?}", env::var("WAYLAND_DISPLAY"));
    
    // 尝试创建最简单的 Tauri 应用
    eprintln!("🚀 尝试创建最简单的 Tauri 应用...");
    
    let result = std::panic::catch_unwind(|| {
        tauri::Builder::default()
            .setup(|_app| {
                eprintln!("✅ Tauri setup 回调被调用");
                Ok(())
            })
            .run(tauri::generate_context!())
    });
    
    match result {
        Ok(app_result) => {
            match app_result {
                Ok(_) => eprintln!("✅ 最小化 Tauri 应用正常退出"),
                Err(e) => {
                    eprintln!("❌ 最小化 Tauri 应用运行失败: {}", e);
                    
                    // 分析错误类型
                    let error_str = e.to_string();
                    if error_str.contains("Could not connect") {
                        eprintln!("🔍 这是连接错误");
                    }
                    if error_str.contains("Unable to init server") {
                        eprintln!("🔍 这是服务器初始化错误");
                    }
                    if error_str.contains("display") {
                        eprintln!("🔍 这是显示相关错误");
                    }
                }
            }
        }
        Err(panic_info) => {
            eprintln!("💥 Tauri 应用发生 panic: {:?}", panic_info);
        }
    }
    
    eprintln!("🏁 最小化测试完成");
}
